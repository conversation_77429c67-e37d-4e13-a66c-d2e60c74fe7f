<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Your Website Title - A Catchy & Descriptive Tagline</title>
    <meta name="description" content="A clear, concise, and compelling description of your webpage's content. This often appears in search results.">
    
    <meta name="keywords" content="keyword1, keyword2, relevant keyword3, your brand">
    
    <meta name="author" content="Your Name or Company Name">

    <!-- <link rel="canonical" href="https://www.yourwebsite.com/this-page"> -->

    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">

    <meta property="og:title" content="Your Website Title for Social Media">
    <meta property="og:description" content="A compelling description for when your page is shared on social platforms.">
    <meta property="og:image" content="https://www.yourwebsite.com/assets/images/social-share-image.jpg">
    <meta property="og:url" content="https://www.yourwebsite.com/this-page">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Your Site Name">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@yourtwitterhandle">
    <meta name="twitter:title" content="Your Website Title for Twitter">
    <meta name="twitter:description" content="A compelling description for when your page is shared on Twitter.">
    <meta name="twitter:image" content="https://www.yourwebsite.com/assets/images/twitter-share-image.jpg">

    <!-- * Structured Data (Schema.org)      * -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Your Site Name",
      "url": "https://www.yourwebsite.com/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.yourwebsite.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <!-- Link to your CSS file -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/libs/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>

    <header class="position-relative z-1">
        <nav class="navbar navbar-expand-md position-absolute top-0 w-100" aria-label="Offcanvas navbar large"> 
            <div class="container-xl"> 
                <button id="btnHamburger" class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasNavbar2" aria-controls="offcanvasNavbar2" aria-label="Toggle navigation"> 
                    <span class="navbar-toggler-icon"></span> 
                </button>
                 
                <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasNavbar2" aria-labelledby="offcanvasNavbar2Label">
                    <div class="offcanvas-header">
                        <h5 class="offcanvas-title" id="offcanvasNavbar2Label">Offcanvas</h5>
                        <button type="button" class="btn-close btn-close-black" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                    </div>
                    <div class="offcanvas-body">
                        <ul class="navbar-nav justify-content-start flex-grow-1 column-gap-2">
                            <li class="nav-item">
                                <a class="nav-link active" aria-current="page" href="#">ผลิตภัณฑ์ของเรา</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">รู้จัก Blanche</a>
                            </li> 
                            <!-- <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Dropdown</a> 
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#">Action</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">Another action</a>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">Something else here</a>
                                    </li>
                                </ul>
                            </li> -->
                        </ul>
                    </div>
                </div>

                <a class="" href="#">
                    <img src="assets/images/logo.webp" alt="Logo" id="logoHeader" width="200" height="30">
                </a> 

                <div id="linkToShop" class="d-flex justify-content-end">
                    <button>
                        <img src="assets/images/shop.webp" alt="shop" width="36" height="36">
                        <span>เลือกซื้อสินค้า</span>
                    </button>
                </div>
            </div>
        </nav>
    </header>
