:root {
  --color-white: #FFFFFF;
  --color-blanche-light-cream: #F4F2E7;
  --color-blanche-dark-brown: #4F4943;
  --color-blanche-light-brown: #DEC7AD;
  --color-blanche-brown: #C3B4A2;
  --color-blanche-gray:#DBDBDB;
  --color-blanche-green:#B7B7AB;
  --color-blanche-light-gray:#1D1B2033;
  --color-black:#1D1B20;
  --ts-300:all 0.3s ease-out;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-Thin.ttf');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-ExtraLight.ttf');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-Light.ttf');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-Regular.ttf');
  font-weight: 400; /* 'normal' is an alias for 400 */
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-Medium.ttf');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-SemiBold.ttf');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'IBM Plex Sans Thai';
  src: url('../fonts/IBM_Plex_Sans_Thai/IBMPlexSansThai-Bold.ttf');
  font-weight: 700; /* 'bold' is an alias for 700 */
  font-style: normal;
}


/* --- Body and Class Definitions --- */
/* Your existing classes will now work as expected */

body {
  font-family: 'IBM Plex Sans Thai', sans-serif;
  font-size: 1rem;
  background: var(--color-white);
  color: var(--color-blanche-dark-brown);
}

.ibm-plex-sans-thai-thin {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 100;
}

.ibm-plex-sans-thai-extralight {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 200;
}

.ibm-plex-sans-thai-light {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 300;
}

.ibm-plex-sans-thai-regular {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 400;
}

.ibm-plex-sans-thai-medium {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 500;
}

.ibm-plex-sans-thai-semibold {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 600;
}

.ibm-plex-sans-thai-bold {
  font-family: "IBM Plex Sans Thai", sans-serif;
  font-weight: 700;
}

a{
  text-decoration: none;
  transition:var(--ts-300);
}
.bg-blanche-light-brown{
  background-color:var(--color-blanche-light-brown);
}
.flex-none{
  flex:none;
}
.text-light-cream{
  color:var(--color-blanche-light-cream);
}
.text-dark-brown{
  color:var(--color-blanche-dark-brown);
}
.text-blanche-green{
  color: var(--color-blanche-green);
}
.text-black{
  color:var(--color-black) !important;
}
.py-section{
  padding-top:120px;
  padding-bottom:120px;
}
#logoHeader{
    width:200px;
    height:auto;
}
#linkToShop > button{
    width:160px;
    height:48px;
    display:flex;
    align-items:center;
    justify-content:center;
    gap:4px;
    border-radius: 100px;
    cursor: pointer;
    background-color:var(--color-blanche-light-cream);
    border:none;
    transition:var(--ts-300);
}
#linkToShop > button > span{
    transform: translateY(2px);
}
#linkToShop > button:hover{
    background-color:var(--color-blanche-dark-brown);
    color:var(--color-white);
}
#btnHamburger{
    margin-right:calc(160px - 56px);
}
.navbar-nav .nav-link, .navbar-nav .nav-link.active{
    color:var(--color-blanche-light-cream);
    border-radius:100px;
    transition: background-color 0.3s ease-out;
}
.navbar-nav .nav-link:hover{
    background-color:#a39688;
}
#btnScrollDown{
  transform: translateY(50%);
  height:48px;
  border:1px solid var(--color-blanche-brown);
  border-radius:100px;
  backdrop-filter: blur(4px);
  background-color:#F4F2E780;
}
.gap-40{
  gap:2.5rem;
}
.row-gap-40{
  row-gap:2.5rem;
}
.content-footer{
  padding-top:120px;
  padding-bottom:120px;
  color: var(--color-blanche-light-cream);
  background-color:var(--color-blanche-dark-brown);
}

.vr{
  background-color: var(--color-blanche-gray);
}

.btn{
  border-radius: 100px;
  height:48px;
}
.btn-blanche-1{
  background-color:transparent;
}
.btn-blanche-1:hover{
  background-color:var(--color-blanche-light-gray);
}
.btn-blanche-2{
  background-color:var(--color-blanche-light-cream);
}
.btn-blanche-2:hover{
  background-color:var(--color-blanche-dark-brown);
  color: var(--color-white);
}
.btn-blanche-3{
  background-color: transparent;
  border:1px solid var(--color-blanche-dark-brown);
}
.btn-blanche-3:hover{
  background-color:var(--color-blanche-dark-brown);
  color: var(--color-white);
}
.link-white{
  color: var(--color-blanche-light-cream);
}
.link-white:hover{
  color: var(--color-blanche-light-brown);
}
#aboutus{
  background-image: url('../images/bg_about.webp');
  background-color:#FFF;
  background-size: cover;
  background-position: center center;
  position:relative;
  padding-top:160px;
  padding-bottom:160px;
}
#aboutus:before{
  content:'';
  background-color: var(--color-white);
  position:absolute;
  width:100%;
  height:100%;
  top:0;
  left:0;
  opacity:.9;
  z-index:0;
}
#aboutus > .container{
  position:relative;
  z-index:1;
}
#title_about{
  padding:42px 0;
}
#title_about > img{
  width:400px;
  max-width:100%;
  height:auto;
}
#videoContainer{
  width:430px;
  max-width: 100%;
  height:auto;
  position:relative;
  display:block;
  margin:auto;
  /* top:-85px;
  left:50%;
  transform: translate(-50%, -0%); */
}
#videoReview{
  width:100%;
  height:auto;
  outline: 8px solid #4A4444;
  background-color:#FFF;
  outline-offset: -8px;
  border-radius: 48px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.25);
  display: block;
  margin-top:-85px;
  margin-bottom:-45px;
}
#btnVideoSound{
  position:absolute;
  bottom:1.5rem;
  right:1.5rem;
  width:48px;
  height:48px;
  border-radius:100%;
  backdrop-filter: blur(4px);
  background-color:var(--color-blanche-light-cream);
  border:1px solid var(--color-blanche-brown);
  overflow: hidden;
  transition:var(--ts-300);
}
#btnVideoSound:hover{
  background-color:var(--color-blanche-brown);
}
#btnVideoSound:after{
  content:'';
  display:block;
  position:absolute;
  top:50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width:24px;
  height:24px;
  background-image: url('../images/icon/muted.svg');
  background-size:contain;
  background-position:center center;  
}
#btnVideoSound[data-muted="false"]:after{
  background-image: url('../images/icon/unmuted.svg');
}

#videopresent{
  background-color: var(--color-blanche-brown);
  position:relative;
  margin-top:85px;
}

#videopresent > .container{
  position:relative;
  max-width:1728px;
  z-index: 1;
}
#videopresent > .container .row .col-xl-6:first-child{
  background-image: url('../images/bg_video.webp');
  background-size:cover;
  background-position:center center;
}
#carouselReview::after, #carouselReview::before{
  content: '';
  width:1.5rem;
  height:100%;
  position: absolute;
  z-index: 99;
}
#carouselReview::before{
  top: 0;
  left: 0;
  background: #C3B4A2;
  background: -webkit-linear-gradient(270deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  background: -moz-linear-gradient(270deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  background: linear-gradient(270deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#C3B4A2", endColorstr="#C3B4A2", GradientType=0);
}
#carouselReview::after{
  top: 0;
  right: 0;
  background: #C3B4A2;
  background: -webkit-linear-gradient(90deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  background: -moz-linear-gradient(90deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  background: linear-gradient(90deg, rgba(195, 180, 162, 0) 0%, rgba(195, 180, 162, 1) 90%, rgba(195, 180, 162, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#C3B4A2", endColorstr="#C3B4A2", GradientType=0);
}
#carouselReview{
  padding: 80px 0px;
}
#carouselReview .swiper-slide{
  padding:0 1.5rem;
}
#carouselReview .swiper-pagination{
  position:relative;
  margin-top:24px;
  bottom:0;
}
#carouselReview .swiper-pagination .swiper-pagination-bullet{
  width:12px;
  height:12px;
  background-color: var(--color-blanche-gray);
  opacity:1;
}
#carouselReview .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active{
  background-color: var(--color-white);
}
.border-user{
  border:1px solid var(--color-blanche-gray);
  border-radius: 50%;
  overflow: hidden;
}
.user-product{
  width:400px;
  max-width:100%;
}

.dt-product{
  row-gap:80px;
  height:566px;
  background-size: cover;
  background-position: center center;
  position:relative;
}
.dt-product::before{
  content:'';
  display:block;
  position:absolute;
  width:100%;
  height:100%;
  top:0;
  left:0;
  z-index:0;
  transition:var(--ts-300);
}
.dt-product:hover::before{
  backdrop-filter: blur(6px);
}
.dt-product > div{
  z-index: 1;
}
.dt-product p{
  margin-bottom:0;
}
.title-dt{
  margin-bottom:0;
  font-size:2.25rem;
}
#productsText{
  position:relative;
  text-align:center;
  z-index: 99;
  pointer-events: none;
}
#productsText .col-auto{
  pointer-events: auto;
}
#carouselProducts .swiper-slide{
  width:416px;
  text-align: center;
}
#carouselProducts .img-product{
  width:auto;
  max-width:100%;
  height:400px;
  display: block;
  margin:0 auto;
  transition: var(--ts-300);
}

#carouselProducts .swiper-slide a:hover .img-product{
  filter: drop-shadow(0 0 .875rem var(--color-blanche-brown));
}

@media (min-width: 768px) {
    header > nav{
        min-height:96px;
    }
    .navbar-expand-md .offcanvas, #linkToShop{
        flex-grow: 0;
        min-width:276px;
    }
    .navbar-expand-md .navbar-nav .nav-link{
        padding:14px 16px;
    }
      #carouselReview::after, #carouselReview::before{
    width:120px;
  }
  #carouselReview .swiper-slide{
    padding:0px calc(120px - 0.75rem);
  }
}
@media (min-width: 1200px){
  #videopresent::before{
  content: '';
  width:50%;
  height:100%;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('../images/bg_video.webp');
  background-size:cover;
  z-index: 0;
}
#productsText{
  position:absolute;
  width:100%;
  height:100%;
  display:flex;
  align-items:center;
}
  #videoContainer{
    position:absolute;
    top:50%;
    left:50%;
    transform: translate(-50%, -50%);
  }
  #videoReview{
    margin:0;
  }

}
@media (min-width: 1400px){
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl{
    max-width: 1512px;
  }

}