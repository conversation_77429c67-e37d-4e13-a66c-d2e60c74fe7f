document.addEventListener('DOMContentLoaded', function() {
    const btnScrollDown = document.getElementById('btnScrollDown');
    const heroSection = document.getElementById('hero');
    const productsSection = document.getElementById('products');
  
    btnScrollDown.addEventListener('click', function() {
      productsSection.scrollIntoView({ behavior: 'smooth' });
    });


  const videoElement = document.getElementById('videoReview');


  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        if (videoElement.paused) {
          console.log('Video is in view, attempting to play... 🎬');
          videoElement.play().then(() => {
            console.log('Playback started successfully! ✅');
          }).catch(error => {
            console.error('Could not play video automatically! ❌', error);
          });
        }
      } else {
        // videoElement.pause();
        console.log('Video is out of view, playback paused.');
      }
    });
  });

  observer.observe(videoElement);

  // Get a reference to the button and the video element
  const soundButton = document.getElementById('btnVideoSound');

  // Add a click event listener to the button
  soundButton.addEventListener('click', () => {
    // Toggle the video's muted property
    videoElement.muted = !videoElement.muted;

    // (Optional) Sync the button's data attribute with the video's state
    soundButton.dataset.muted = videoElement.muted;

    // (Optional) You can also change the button's appearance
    if (videoElement.muted) {
      soundButton.dataset.muted = 'true'; // Or show a muted icon
    } else {
      soundButton.dataset.muted = 'false'; // Or show an unmuted icon
    }
  });

});